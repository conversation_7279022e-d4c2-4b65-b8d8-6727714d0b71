import io.shiftleft.codepropertygraph.generated.nodes._
import io.shiftleft.semanticcpg.language._

/**
 * 自动路径追踪器
 * 基于你的手动追踪思路，使用递归的 .in.l 和 .out.l 来自动发现路径
 * 针对case3.php的例子：从greet方法到$_GET的source点
 */

println("=== 自动CPG路径追踪器 ===")

// 定义source点和sink点
def findSourceNodes(cpg: Cpg) = {
  println("查找Source点...")
  val getSources = cpg.call.code(".*\\$_GET.*").l

  // 过滤掉无用的source节点
  val filteredSources = filterUsefulNodes(getSources)
  showFilterStats(getSources, filteredSources, "source")

  println(s"找到 ${filteredSources.length} 个有效的 $$_GET 相关source点")
  // 只显示前3个source点，避免输出过多
  filteredSources.take(3).foreach { source =>
    try {
      val astNode = source.asInstanceOf[AstNode]
      println(s"  Source: ${astNode.code} (行号: ${astNode.lineNumber.getOrElse("N/A")})")
    } catch {
      case _: Exception =>
        println(s"  Source: [节点${source.id}] (信息获取失败)")
    }
  }
  if (filteredSources.length > 3) {
    println(s"  ... 还有 ${filteredSources.length - 3} 个source点")
  }
  filteredSources
}

def findSinkNodes(cpg: Cpg) = {
  println("\n查找Sink点...")
  val systemCalls = cpg.call.name("system").l
  val greetMethods = cpg.method.name("greet").l
  val allSinks = (systemCalls ++ greetMethods).distinct

  // 过滤掉无用的sink节点
  val filteredSinks = filterUsefulNodes(allSinks)
  showFilterStats(allSinks, filteredSinks, "sink")

  println(s"找到 ${filteredSinks.length} 个有效sink点")
  // 只显示前3个sink点，避免输出过多
  filteredSinks.take(3).foreach { sink =>
    try {
      val astNode = sink.asInstanceOf[AstNode]
      println(s"  Sink: ${astNode.code} (行号: ${astNode.lineNumber.getOrElse("N/A")})")
    } catch {
      case _: Exception =>
        println(s"  Sink: [节点${sink.id}] (信息获取失败)")
    }
  }
  if (filteredSinks.length > 3) {
    println(s"  ... 还有 ${filteredSinks.length - 3} 个sink点")
  }
  filteredSinks
}

// 过滤无用节点的函数 - 增强版，专门过滤掉无意义的输出
def filterUsefulNodes(nodes: List[flatgraph.GNode]): List[flatgraph.GNode] = {
  nodes.filter { node =>
    try {
      val astNode = node.asInstanceOf[AstNode]
      val code = astNode.code
      val nodeType = astNode.getClass.getSimpleName

      // 过滤掉无用的节点
      !(code == "<empty>" ||
        code.trim.isEmpty ||
        code == "<global>" ||
        code == "<init>" ||
        code == "<clinit>" ||
        code.startsWith("<operator>") ||
        nodeType.contains("Unknown") ||
        nodeType.contains("Block") ||
        // 新增：过滤掉迭代器相关的无意义节点
        code.contains("@iter_tmp") ||
        code.contains("->current()") ||
        code.contains("->next()") ||
        code.contains("->key()") ||
        code.contains("->valid()") ||
        code.contains("->rewind()") ||
        // 过滤掉框架内部方法调用
        code.contains("think\\") ||
        code.contains("fast\\") ||
        code.contains("app\\common\\library\\") ||
        code.contains("Overtrue\\") ||
        code.contains("ReflectionClass") ||
        code.contains("ReflectionMethod")
        // 过滤掉一些常见的无意义节点
        //(code.length < 3 && !code.contains("GET") && !code.contains("system") && !code.contains("post"))
        )
    } catch {
      case _: Exception => false // 如果无法转换为AstNode，也过滤掉
    }
  }
}

// 显示过滤统计信息
def showFilterStats(originalNodes: List[flatgraph.GNode], filteredNodes: List[flatgraph.GNode], nodeType: String): Unit = {
  val filtered = originalNodes.length - filteredNodes.length
  if (filtered > 0) {
    println(s"  过滤掉 $filtered 个无用的${nodeType}节点 (${originalNodes.length} -> ${filteredNodes.length})")
  }
}

// 路径链数据结构
case class PathChain(nodes: List[flatgraph.GNode], isComplete: Boolean, endReason: String, depth: Int)

// 新的路径追踪函数 - 记录每条独立的完整路径
def traceAllIndividualPaths(startNode: flatgraph.GNode, direction: String, maxDepth: Int): List[PathChain] = {
  var allPaths = List[PathChain]()

  def traceRecursive(currentNode: flatgraph.GNode, currentPath: List[flatgraph.GNode], depth: Int): Unit = {
    // 检查深度限制
    if (depth >= maxDepth) {
      allPaths = allPaths :+ PathChain(currentPath, false, "达到最大深度", depth)
      return
    }

    // 获取下一层节点
    val nextNodes = try {
      if (direction == "in") {
        currentNode.in.l
      } else {
        currentNode.out.l
      }
    } catch {
      case _: Exception => List()
    }

    // 过滤有用节点
    val filteredNextNodes = filterUsefulNodes(nextNodes)

    if (filteredNextNodes.isEmpty) {
      // 没有更多节点，路径结束
      allPaths = allPaths :+ PathChain(currentPath, true, "无更多节点", depth)
    } else {
      // 为每个下一层节点创建独立路径
      filteredNextNodes.foreach { nextNode =>
        val newPath = currentPath :+ nextNode
        traceRecursive(nextNode, newPath, depth + 1)
      }
    }
  }

  // 从起始节点开始追踪
  traceRecursive(startNode, List(startNode), 0)
  allPaths
}

// 过滤出有效路径（从sink到source）
def filterValidPaths(allPaths: List[PathChain], sinkName: String, sourceName: String): List[PathChain] = {
  allPaths.filter { pathChain =>
    val nodes = pathChain.nodes
    if (nodes.length < 2) {
      false
    } else {
      // 检查起始节点是否匹配sink
      val firstNodeMatches = try {
        val firstNode = nodes.head.asInstanceOf[AstNode]
        firstNode.code.contains(sinkName) ||
        (firstNode match {
          case call: Call => call.name.contains(sinkName)
          case method: Method => method.name.contains(sinkName)
          case _ => false
        })
      } catch {
        case _: Exception => false
      }

      // 检查结束节点是否匹配source
      val lastNodeMatches = try {
        val lastNode = nodes.last.asInstanceOf[AstNode]
        lastNode.code.contains(sourceName) ||
        (lastNode match {
          case call: Call => call.name.contains(sourceName) || call.code.contains(sourceName)
          case id: Identifier => id.name.contains(sourceName)
          case _ => false
        })
      } catch {
        case _: Exception => false
      }

      firstNodeMatches && lastNodeMatches
    }
  }
}

// 递归追踪路径的核心函数（保留原有功能）
def tracePathRecursive(currentNodes: List[flatgraph.GNode], targetNodes: List[flatgraph.GNode],
                      direction: String, path: List[List[flatgraph.GNode]],
                      maxDepth: Int, currentDepth: Int): List[List[flatgraph.GNode]] = {

  if (currentDepth >= maxDepth) {
    return path
  }

  if (currentNodes.isEmpty) {
    return path
  }

  // 过滤当前节点中的无用节点
  val filteredCurrentNodes = filterUsefulNodes(currentNodes)

  // 检查是否到达目标节点
  val foundTargets = filteredCurrentNodes.filter { node =>
    targetNodes.exists(_.id == node.id)
  }

  if (foundTargets.nonEmpty) {
    // 记录找到的路径
    val newPath = path :+ filteredCurrentNodes
    return newPath
  }

  // 继续追踪下一层
  try {
    val nextNodes = if (direction == "in") {
      currentNodes.flatMap(_.in.l).distinct
    } else {
      currentNodes.flatMap(_.out.l).distinct
    }

    // 过滤下一层节点
    val filteredNextNodes = filterUsefulNodes(nextNodes)

    if (filteredNextNodes.nonEmpty) {
      val newPath = if (filteredCurrentNodes.nonEmpty) path :+ filteredCurrentNodes else path
      tracePathRecursive(filteredNextNodes, targetNodes, direction, newPath, maxDepth, currentDepth + 1)
    } else {
      if (filteredCurrentNodes.nonEmpty) path :+ filteredCurrentNodes else path
    }
  } catch {
    case e: Exception =>
      if (filteredCurrentNodes.nonEmpty) path :+ filteredCurrentNodes else path
  }
}

// 获取节点的简要信息
def getNodeInfo(node: flatgraph.GNode): String = {
  try {
    val astNode = node.asInstanceOf[AstNode]
    val nodeType = astNode.getClass.getSimpleName
    val code = astNode.code.take(50) // 限制代码长度
    val lineNum = astNode.lineNumber.getOrElse("N/A")

    val name = astNode match {
      case call: Call => call.name
      case method: Method => method.name
      case id: Identifier => id.name
      case _ => "N/A"
    }

    s"ID:${node.id} | Type:$nodeType | Name:$name | Code:$code | Line:$lineNum"
  } catch {
    case _: Exception =>
      s"ID:${node.id} | [信息获取失败]"
  }
}

// 保存路径信息到文件
def savePathToFile(pathSteps: List[List[flatgraph.GNode]], direction: String, startNode: flatgraph.GNode, endNode: flatgraph.GNode, filename: String): Unit = {
  try {
    import java.io.{File, PrintWriter}
    val writer = new PrintWriter(new File(filename))

    writer.println(s"=== CPG路径追踪结果 (${direction}方向) ===")
    writer.println(s"生成时间: ${java.time.LocalDateTime.now()}")
    writer.println(s"起始节点: ${getNodeInfo(startNode)}")
    writer.println(s"目标节点: ${getNodeInfo(endNode)}")
    writer.println(s"路径总长度: ${pathSteps.length} 步")
    writer.println()

    pathSteps.zipWithIndex.foreach { case (stepNodes, stepIndex) =>
      writer.println(s"步骤 ${stepIndex + 1}: ${stepNodes.length} 个节点")
      writer.println("=" * 50)

      stepNodes.zipWithIndex.foreach { case (node, nodeIndex) =>
        try {
          val astNode = node.asInstanceOf[AstNode]
          writer.println(s"节点 ${nodeIndex + 1}:")
          writer.println(s"  ID: ${node.id}")
          writer.println(s"  类型: ${astNode.getClass.getSimpleName}")
          writer.println(s"  代码: ${astNode.code}")
          writer.println(s"  行号: ${astNode.lineNumber.getOrElse("N/A")}")

          astNode match {
            case call: Call =>
              writer.println(s"  方法名: ${call.name}")
              writer.println(s"  方法全名: ${call.methodFullName}")
            case method: Method =>
              writer.println(s"  方法名: ${method.name}")
              writer.println(s"  方法全名: ${method.fullName}")
            case id: Identifier =>
              writer.println(s"  标识符名: ${id.name}")
            case _ =>
          }
          writer.println()
        } catch {
          case e: Exception =>
            writer.println(s"节点 ${nodeIndex + 1}: [信息获取失败] ${e.getMessage}")
        }
      }
      writer.println()
    }

    writer.close()
    println(s"路径信息已保存到文件: $filename")
  } catch {
    case e: Exception =>
      println(s"保存文件失败: ${e.getMessage}")
  }
}

// 获取节点的文件信息
def getFileInfo(node: flatgraph.GNode): String = {
  try {
    val astNode = node.asInstanceOf[AstNode]
    astNode.file.name.headOption.getOrElse("unknown")
  } catch {
    case _: Exception => "unknown"
  }
}

// 打印表格格式的路径信息
def printTablePath(pathSteps: List[List[flatgraph.GNode]], direction: String, startNode: flatgraph.GNode, endNode: flatgraph.GNode): Unit = {
  println(s"\n=== CPG边链路径表格 (${direction}方向) ===")
  println(s"起始节点: ${getNodeInfo(startNode)}")
  println(s"目标节点: ${getNodeInfo(endNode)}")
  println(s"路径总长度: ${pathSteps.length} 步")
  println()

  // 表格头部
  val headerFormat = "%-6s %-8s %-15s %-20s %-70s %-8s %-20s"
  println(headerFormat.format("步骤", "节点ID", "节点类型", "名称", "代码", "行号", "文件"))
  println("=" * 130)

  var globalNodeIndex = 1
  pathSteps.zipWithIndex.foreach { case (stepNodes, stepIndex) =>
    stepNodes.foreach { node =>
      try {
        val astNode = node.asInstanceOf[AstNode]
        val nodeType = astNode.getClass.getSimpleName
        val code = astNode.code.take(50) // 限制代码长度以适应表格
        val lineNum = astNode.lineNumber.getOrElse("N/A").toString
        val fileName = getFileInfo(node)

        val name = astNode match {
          case call: Call => call.name
          case method: Method => method.name
          case id: Identifier => id.name
          case _ => "N/A"
        }

        println(headerFormat.format(
          s"${stepIndex + 1}",
          node.id.toString,
          nodeType,
          name.take(18), // 限制名称长度
          code,
          lineNum,
          fileName.split("/").lastOption.getOrElse(fileName) // 只显示文件名，不显示完整路径
        ))

        globalNodeIndex += 1
      } catch {
        case e: Exception =>
          println(headerFormat.format(
            s"${stepIndex + 1}",
            node.id.toString,
            "ERROR",
            "N/A",
            "[信息获取失败]",
            "N/A",
            "unknown"
          ))
      }
    }

    // 在每个步骤之间添加分隔线
    if (stepIndex < pathSteps.length - 1) {
      println("-" * 130)
    }
  }

  println("=" * 130)
  println(s"总计: ${globalNodeIndex - 1} 个节点")
}

// 保存表格格式到文件
def saveTableToFile(pathSteps: List[List[flatgraph.GNode]], direction: String, startNode: flatgraph.GNode, endNode: flatgraph.GNode, filename: String): Unit = {
  try {
    import java.io.{File, PrintWriter}
    val writer = new PrintWriter(new File(filename))

    writer.println(s"=== CPG边链路径表格 (${direction}方向) ===")
    writer.println(s"生成时间: ${java.time.LocalDateTime.now()}")
    writer.println(s"起始节点: ${getNodeInfo(startNode)}")
    writer.println(s"目标节点: ${getNodeInfo(endNode)}")
    writer.println(s"路径总长度: ${pathSteps.length} 步")
    writer.println()

    // 表格头部
    val headerFormat = "%-6s %-8s %-15s %-20s %-50s %-8s %-20s"
    writer.println(headerFormat.format("步骤", "节点ID", "节点类型", "名称", "代码", "行号", "文件"))
    writer.println("=" * 130)

    var globalNodeIndex = 1
    pathSteps.zipWithIndex.foreach { case (stepNodes, stepIndex) =>
      stepNodes.foreach { node =>
        try {
          val astNode = node.asInstanceOf[AstNode]
          val nodeType = astNode.getClass.getSimpleName
          val code = astNode.code.take(48)
          val lineNum = astNode.lineNumber.getOrElse("N/A").toString
          val fileName = getFileInfo(node)

          val name = astNode match {
            case call: Call => call.name
            case method: Method => method.name
            case id: Identifier => id.name
            case _ => "N/A"
          }

          writer.println(headerFormat.format(
            s"${stepIndex + 1}",
            node.id.toString,
            nodeType,
            name.take(18),
            code,
            lineNum,
            fileName.split("/").lastOption.getOrElse(fileName)
          ))

          globalNodeIndex += 1
        } catch {
          case e: Exception =>
            writer.println(headerFormat.format(
              s"${stepIndex + 1}",
              node.id.toString,
              "ERROR",
              "N/A",
              "[信息获取失败]",
              "N/A",
              "unknown"
            ))
        }
      }

      if (stepIndex < pathSteps.length - 1) {
        writer.println("-" * 130)
      }
    }

    writer.println("=" * 130)
    writer.println(s"总计: ${globalNodeIndex - 1} 个节点")

    writer.close()
    println(s"表格路径信息已保存到文件: $filename")
  } catch {
    case e: Exception =>
      println(s"保存文件失败: ${e.getMessage}")
  }
}

// 主要的追踪函数
def autoTracePaths(cpg: Cpg): Unit = {
  println("\n=== 开始自动路径追踪 ===")
  
  val sources = findSourceNodes(cpg)
  val sinks = findSinkNodes(cpg)
  
  if (sources.isEmpty || sinks.isEmpty) {
    println("未找到source或sink点，无法进行追踪")
    return
  }
  
  // 从sink到source的反向追踪
  println(s"\n=== 从Sink到Source的反向追踪 ===")
  sinks.foreach { sink =>
    try {
      val sinkNode = sink.asInstanceOf[AstNode]
      println(s"\n开始从sink追踪: ${sinkNode.code}")
    } catch {
      case _: Exception =>
        println(s"\n开始从sink追踪: [无法获取代码信息]")
    }
    
    val pathToSource = tracePathRecursive(
      currentNodes = List(sink),
      targetNodes = sources,
      direction = "in",
      path = List.empty,
      maxDepth = 10,
      currentDepth = 0
    )
    
    if (pathToSource.nonEmpty) {
      // 找到第一个source节点作为目标
      val targetSource = sources.head
      printTablePath(pathToSource, "反向(in)", sink, targetSource)

      // 保存到文件
      saveTableToFile(pathToSource, "反向(in)", sink, targetSource, s"path_sink_${sink.id}_to_source_${targetSource.id}.txt")
    } else {
      println("未找到到source的路径")
    }
  }
  
  // 从source到sink的正向追踪
  println(s"\n=== 从Source到Sink的正向追踪 ===")
  sources.foreach { source =>
    try {
      val sourceNode = source.asInstanceOf[AstNode]
      println(s"\n开始从source追踪: ${sourceNode.code}")
    } catch {
      case _: Exception =>
        println(s"\n开始从source追踪: [无法获取代码信息]")
    }
    
    val pathToSink = tracePathRecursive(
      currentNodes = List(source),
      targetNodes = sinks,
      direction = "out",
      path = List.empty,
      maxDepth = 10,
      currentDepth = 0
    )
    
    if (pathToSink.nonEmpty) {
      // 找到第一个sink节点作为目标
      val targetSink = sinks.head
      printTablePath(pathToSink, "正向(out)", source, targetSink)

      // 保存到文件
      saveTableToFile(pathToSink, "正向(out)", source, targetSink, s"path_source_${source.id}_to_sink_${targetSink.id}.txt")
    } else {
      println("未找到到sink的路径")
    }
  }
}

// 手动指定节点进行追踪
def manualTrace(cpg: Cpg, startNodeName: String, targetNodeName: String, direction: String, maxDepth: Int = 8): Unit = {
  println(s"\n=== 手动追踪: $startNodeName -> $targetNodeName (${direction}方向) ===")
  
  // 查找起始节点
  val allStartNodes = (cpg.method.name(startNodeName).l ++
                      cpg.call.name(startNodeName).l ++
                      cpg.identifier.name(startNodeName).l).distinct
  val startNodes = filterUsefulNodes(allStartNodes)

  // 查找目标节点
  val allTargetNodes = (cpg.method.name(targetNodeName).l ++
                       cpg.call.name(targetNodeName).l ++
                       cpg.identifier.name(targetNodeName).l ++
                       cpg.call.code(s".*$targetNodeName.*").l).distinct
  val targetNodes = filterUsefulNodes(allTargetNodes)
  
  if (startNodes.isEmpty) {
    println(s"未找到起始节点: $startNodeName")
    return
  }
  
  if (targetNodes.isEmpty) {
    println(s"未找到目标节点: $targetNodeName")
    return
  }
  
  println(s"起始节点: ${startNodes.length} 个")
  startNodes.foreach { node =>
    try {
      val astNode = node.asInstanceOf[AstNode]
      println(s"  ${astNode.code}")
    } catch {
      case _: Exception =>
        println(s"  [无法获取代码信息]")
    }
  }

  println(s"目标节点: ${targetNodes.length} 个")
  targetNodes.foreach { node =>
    try {
      val astNode = node.asInstanceOf[AstNode]
      println(s"  ${astNode.code}")
    } catch {
      case _: Exception =>
        println(s"  [无法获取代码信息]")
    }
  }
  
  startNodes.foreach { startNode =>
    val path = tracePathRecursive(
      currentNodes = List(startNode),
      targetNodes = targetNodes,
      direction = direction,
      path = List.empty,
      maxDepth = maxDepth,
      currentDepth = 0
    )
    
    if (path.nonEmpty) {
      val firstTarget = targetNodes.head
      printTablePath(path, direction, startNode, firstTarget)

      // 保存到文件
      saveTableToFile(path, direction, startNode, firstTarget, s"manual_trace_${startNode.id}_to_${firstTarget.id}.txt")
    }
  }
}

// 简单的原始数据流显示函数
def showRawDataFlow(cpg: Cpg, sinkName: String, sourceName: String): Unit = {
  println(s"\n=== 原始数据流分析: $sinkName <- $sourceName ===")

  try {
    import io.joern.dataflowengineoss.language._

    // 查找source和sink节点
    val sourceNodes = if (sourceName.contains("GET")) {
      cpg.call.code(".*\\$_GET.*").l ++ cpg.identifier.name("_GET").l
    } else {
      cpg.call.code(s".*$sourceName.*").l ++ cpg.identifier.name(sourceName).l
    }

    val sinkNodes = cpg.call.name(sinkName).l

    println(s"找到 ${sourceNodes.size} 个source节点, ${sinkNodes.size} 个sink节点")

    if (sourceNodes.nonEmpty && sinkNodes.nonEmpty) {
      // 执行数据流分析
      val flows = cpg.call.name(sinkName).reachableByFlows(sourceNodes)

      println(s"\n✅ 找到 ${flows.size} 条数据流路径")

      // 直接显示原始数据流
      flows.zipWithIndex.foreach { case (flow, index) =>
        println(s"\n--- 数据流路径 ${index + 1} ---")
        println(s"路径包含 ${flow.elements.size} 个节点:")

        flow.elements.zipWithIndex.foreach { case (element, i) =>
          try {
            val astNode = element.asInstanceOf[AstNode]
            val lineNum = astNode.lineNumber.getOrElse("?")
            val fileName = astNode.file.name.headOption.getOrElse("unknown")
            println(s"  ${i + 1}. ${astNode.code} (行:$lineNum, 文件:${fileName.split("/").last})")
          } catch {
            case _: Exception =>
              println(s"  ${i + 1}. [节点ID:${element.id}] [无法获取信息]")
          }
        }
      }

      if (flows.isEmpty) {
        println("❌ 未找到数据流路径")
      }
    } else {
      println("❌ 未找到足够的source或sink节点")
    }
  } catch {
    case e: Exception =>
      println(s"❌ 数据流分析失败: ${e.getMessage}")
      e.printStackTrace()
  }
}

// 执行自动追踪
//autoTracePaths(cpg)

// 提供手动追踪的示例
println("\n=== 手动追踪示例 ===")
println("可以使用以下命令进行手动追踪:")
println("manualTrace(cpg, \"greet\", \"$$_GET\", \"in\", 10)")
println("manualTrace(cpg, \"greet\", \"system\", \"out\", 5)")
println("\n=== 原始数据流显示 ===")
println("可以使用以下命令查看原始数据流:")
println("showRawDataFlow(cpg, \"system\", \"$$_GET\")")
println("showRawDataFlow(cpg, \"greet\", \"$$_GET\")")

// 简化的表格输出（只显示关键信息）
def printSimpleTable(pathSteps: List[List[flatgraph.GNode]], direction: String): Unit = {
  println(s"\n=== 简化CPG边链表格 (${direction}方向) ===")

  val headerFormat = "%-6s %-8s %-15s %-40s %-8s %-15s"
  println(headerFormat.format("步骤", "节点ID", "类型", "代码", "行号", "文件"))
  println("=" * 100)

  pathSteps.zipWithIndex.foreach { case (stepNodes, stepIndex) =>
    stepNodes.foreach { node =>
      try {
        val astNode = node.asInstanceOf[AstNode]
        val nodeType = astNode.getClass.getSimpleName.replace("Impl", "")
        val code = astNode.code.take(38)
        val lineNum = astNode.lineNumber.getOrElse("N/A").toString
        val fileName = getFileInfo(node).split("/").lastOption.getOrElse("unknown")

        println(headerFormat.format(
          s"${stepIndex + 1}",
          node.id.toString,
          nodeType,
          code,
          lineNum,
          fileName
        ))
      } catch {
        case _: Exception =>
          println(headerFormat.format(
            s"${stepIndex + 1}",
            node.id.toString,
            "ERROR",
            "[获取失败]",
            "N/A",
            "unknown"
          ))
      }
    }
  }
  println("=" * 100)
}

// 快速追踪函数（使用简化表格）
def quickTrace(cpg: Cpg, startName: String, targetName: String, direction: String = "in", maxDepth: Int = 8): Unit = {
  println(s"\n=== 快速追踪: $startName -> $targetName ===")

  val allStartNodes = (cpg.method.name(startName).l ++ cpg.call.name(startName).l).distinct
  val startNodes = filterUsefulNodes(allStartNodes)

  val allTargetNodes = (cpg.call.code(s".*$targetName.*").l ++ cpg.identifier.name(targetName).l).distinct
  val targetNodes = filterUsefulNodes(allTargetNodes)

  if (startNodes.isEmpty) {
    println(s"❌ 未找到起始节点: $startName")
    return
  }

  if (targetNodes.isEmpty) {
    println(s"❌ 未找到目标节点: $targetName")
    return
  }

  println(s"✅ 起始节点: ${startNodes.length} 个")
  println(s"✅ 目标节点: ${targetNodes.length} 个")

  startNodes.foreach { startNode =>
    val path = tracePathRecursive(
      currentNodes = List(startNode),
      targetNodes = targetNodes,
      direction = direction,
      path = List.empty,
      maxDepth = maxDepth,
      currentDepth = 0
    )

    if (path.nonEmpty) {
      printSimpleTable(path, direction)
    } else {
      println("❌ 未找到路径")
    }
  }
}

// 执行一个具体的手动追踪示例
// 调试函数：显示被过滤的节点
def showFilteredNodes(cpg: Cpg, nodeType: String): Unit = {
  println(s"\n=== 调试：查看被过滤的${nodeType}节点 ===")

  val allNodes = nodeType match {
    case "system" => cpg.call.name("system").l
    case "greet" => cpg.method.name("greet").l
    case "GET" => cpg.call.code(".*\\$_GET.*").l
    case _ => List()
  }

  val filteredNodes = filterUsefulNodes(allNodes)
  val removedNodes = allNodes.filterNot(node => filteredNodes.exists(_.id == node.id))

  println(s"原始节点: ${allNodes.length} 个")
  println(s"有效节点: ${filteredNodes.length} 个")
  println(s"被过滤节点: ${removedNodes.length} 个")

  if (removedNodes.nonEmpty) {
    println("\n被过滤的节点:")
    removedNodes.foreach { node =>
      try {
        val astNode = node.asInstanceOf[AstNode]
        println(s"  节点${node.id}: ${astNode.getClass.getSimpleName} - '${astNode.code}'")
      } catch {
        case _: Exception =>
          println(s"  节点${node.id}: [无法获取信息]")
      }
    }
  }
}

// 新的独立路径追踪函数
def traceIndividualPaths(cpg: Cpg, sinkName: String, sourceName: String, direction: String = "in", maxDepth: Int = 10): Unit = {
  println(s"\n=== 独立路径追踪: $sinkName -> $sourceName ===")

  // 找到所有sink节点
  val allSinkNodes = (cpg.call.name(sinkName).l ++ cpg.method.name(sinkName).l).distinct
  val filteredSinks = filterUsefulNodes(allSinkNodes)

  if (filteredSinks.isEmpty) {
    println(s"❌ 未找到sink节点: $sinkName")
    return
  }

  println(s"✅ 找到 ${filteredSinks.length} 个sink节点")

  var allValidPaths = List[PathChain]()

  // 为每个sink节点追踪所有可能路径
  filteredSinks.foreach { sinkNode =>
    try {
      val astNode = sinkNode.asInstanceOf[AstNode]
      println(s"\n--- 从sink节点开始追踪: ${astNode.code} ---")
    } catch {
      case _: Exception =>
        println(s"\n--- 从sink节点开始追踪: [节点${sinkNode.id}] ---")
    }

    // 获取所有路径
    val allPaths = traceAllIndividualPaths(sinkNode, direction, maxDepth)
    println(s"总共找到 ${allPaths.length} 条路径")

    // 过滤出有效路径
    val validPaths = filterValidPaths(allPaths, sinkName, sourceName)
    println(s"其中有效路径: ${validPaths.length} 条")

    allValidPaths = allValidPaths ++ validPaths

    // 显示前几条有效路径
    validPaths.take(3).zipWithIndex.foreach { case (pathChain, index) =>
      println(s"\n  有效路径 ${index + 1} (深度: ${pathChain.depth}, 结束原因: ${pathChain.endReason}):")
      printPathChain(pathChain)
    }

    if (validPaths.length > 3) {
      println(s"  ... 还有 ${validPaths.length - 3} 条路径")
    }
  }

  // 保存所有有效路径到文件
  if (allValidPaths.nonEmpty) {
    saveAllPathsToFile(allValidPaths, sinkName, sourceName, direction)
  }
}

// 打印单条路径链
def printPathChain(pathChain: PathChain): Unit = {
  val headerFormat = "%-6s %-8s %-15s %-40s %-8s %-15s"

  pathChain.nodes.zipWithIndex.foreach { case (node, index) =>
    try {
      val astNode = node.asInstanceOf[AstNode]
      val nodeType = astNode.getClass.getSimpleName.replace("Impl", "")
      val code = astNode.code.take(38)
      val lineNum = astNode.lineNumber.getOrElse("N/A").toString
      val fileName = getFileInfo(node).split("/").lastOption.getOrElse("unknown")

      println(headerFormat.format(
        s"${index + 1}",
        node.id.toString,
        nodeType,
        code,
        lineNum,
        fileName
      ))
    } catch {
      case _: Exception =>
        println(headerFormat.format(
          s"${index + 1}",
          node.id.toString,
          "ERROR",
          "[获取失败]",
          "N/A",
          "unknown"
        ))
    }
  }
}

// 保存所有路径到文件
def saveAllPathsToFile(allPaths: List[PathChain], sinkName: String, sourceName: String, direction: String): Unit = {
  try {
    import java.io.{File, PrintWriter}
    val filename = s"individual_paths_${sinkName}_to_${sourceName}_${direction}.txt"
    val writer = new PrintWriter(new File(filename))

    writer.println(s"=== 独立路径追踪结果 ===")
    writer.println(s"生成时间: ${java.time.LocalDateTime.now()}")
    writer.println(s"Sink: $sinkName")
    writer.println(s"Source: $sourceName")
    writer.println(s"方向: $direction")
    writer.println(s"总路径数: ${allPaths.length}")
    writer.println()

    val headerFormat = "%-6s %-8s %-15s %-40s %-8s %-15s"

    allPaths.zipWithIndex.foreach { case (pathChain, pathIndex) =>
      writer.println(s"路径 ${pathIndex + 1} (深度: ${pathChain.depth}, 结束: ${pathChain.endReason})")
      writer.println("=" * 100)
      writer.println(headerFormat.format("步骤", "节点ID", "类型", "代码", "行号", "文件"))
      writer.println("-" * 100)

      pathChain.nodes.zipWithIndex.foreach { case (node, index) =>
        try {
          val astNode = node.asInstanceOf[AstNode]
          val nodeType = astNode.getClass.getSimpleName.replace("Impl", "")
          val code = astNode.code.take(38)
          val lineNum = astNode.lineNumber.getOrElse("N/A").toString
          val fileName = getFileInfo(node).split("/").lastOption.getOrElse("unknown")

          writer.println(headerFormat.format(
            s"${index + 1}",
            node.id.toString,
            nodeType,
            code,
            lineNum,
            fileName
          ))
        } catch {
          case _: Exception =>
            writer.println(headerFormat.format(
              s"${index + 1}",
              node.id.toString,
              "ERROR",
              "[获取失败]",
              "N/A",
              "unknown"
            ))
        }
      }
      writer.println()
    }

    writer.close()
    println(s"\n✅ 所有独立路径已保存到文件: $filename")
  } catch {
    case e: Exception =>
      println(s"❌ 保存文件失败: ${e.getMessage}")
  }
}

// 简化的混合追踪方法
def hybridTrace(cpg: Cpg, sinkName: String, sourceName: String, maxDepth: Int = 10): Unit = {
  println(s"\n=== 混合追踪方法: $sinkName -> $sourceName ===")

  // 方法1: 数据流分析 + CPG反向追踪（主要方法）
  println("\n--- 主要方法: 完整调用链重建 ---")
  reconstructCallChain(cpg, sinkName, sourceName)
}

// 删除：performDataFlowAnalysis() 和 analyzeReturnValueFlow() - 功能已整合到主要方法中

// 数据结构：调用链节点（全局定义）
case class ChainNode(
  nodeId: Long,
  nodeType: String,
  code: String,
  methodName: String,
  className: String,
  fileName: String,
  lineNumber: String,
  role: String // "source", "sink", "intermediate", "return", "call"
)

// 完整调用链重建（基于数据流的精确版本）
def reconstructCallChain(cpg: Cpg, sinkName: String, sourceName: String): Unit = {
  println("重建完整调用链（基于数据流分析）...")

  var allNodes = List[ChainNode]()

  // 1. 先收集source和sink节点
  println("\n收集source和sink节点:")

  // 收集source节点 - 更精确的匹配
  val allSourceNodes = if (sourceName.contains("GET")) {
    cpg.call.code(".*\\$_GET.*").l ++ cpg.identifier.name("_GET").l
  } else if (sourceName.contains("POST")) {
    cpg.call.code(".*\\$_POST.*").l ++ cpg.identifier.name("_POST").l
  } else {
    cpg.call.code(s"$sourceName").l ++ cpg.identifier.name(sourceName).l
  }

  // 收集sink节点
  val allSinkNodes = cpg.call.name(sinkName).l

  // 过滤节点
  val filteredSourceNodes = filterUsefulNodes(allSourceNodes)
  val filteredSinkNodes = filterUsefulNodes(allSinkNodes)

  println(s"找到 ${filteredSourceNodes.size} 个source节点, ${filteredSinkNodes.size} 个sink节点")

  // 2. 使用数据流分析找到真正有连接的路径
  println("\n进行数据流分析...")

  try {
    import io.joern.dataflowengineoss.language._
    val flows = cpg.call.name(sinkName).reachableByFlows(filteredSourceNodes)

    if (flows.nonEmpty) {
      println(s"✅ 数据流分析找到 ${flows.size} 条有效路径")

      // 3. 从数据流中提取真正相关的节点
      val relevantNodeIds = flows.flatMap(_.elements.map(_.id)).toSet

      println(s"数据流路径包含 ${relevantNodeIds.size} 个相关节点")

      // 4. 只收集在数据流路径上的节点
      // 首先显示原始的数据流输出
      println("\n=== 原始数据流输出 ===")
      flows.zipWithIndex.foreach { case (flow, flowIndex) =>
        println(s"\n数据流路径 ${flowIndex + 1}:")
        println(s"路径长度: ${flow.elements.size} 个节点")
        println("完整路径:")
        flow.elements.zipWithIndex.foreach { case (element, elementIndex) =>
          try {
            val astNode = element.asInstanceOf[AstNode]
            val lineNum = astNode.lineNumber.getOrElse("?")
            val fileName = astNode.file.name.headOption.getOrElse("unknown").split("/").lastOption.getOrElse("unknown")
            println(s"  ${elementIndex + 1}. [ID:${element.id}] ${astNode.code} (行:$lineNum, 文件:$fileName)")
          } catch {
            case _: Exception =>
              println(s"  ${elementIndex + 1}. [ID:${element.id}] [无法获取节点信息]")
          }
        }
        println("-" * 80)
      }

      // 然后进行格式化处理
      println("\n=== 格式化处理数据流 ===")
      flows.foreach { flow =>
        flow.elements.foreach { element =>
          try {
            val astNode = element.asInstanceOf[AstNode]
            val method = astNode match {
              case call: Call => call.method
              case id: Identifier => id.method
              case ret: Return => ret.method
              case _ =>
                cpg.method.filter(_.ast.l.exists(_.id == astNode.id)).headOption.getOrElse(null)
            }

            if (method != null) {
              val className = method.typeDecl.name.headOption.getOrElse("unknown")
              val fileName = astNode.file.name.headOption.getOrElse("unknown")

              // 确定节点角色
              val role = if (filteredSourceNodes.exists(_.id == element.id)) {
                "source"
              } else if (filteredSinkNodes.exists(_.id == element.id)) {
                "sink"
              } else if (element.isInstanceOf[Return]) {
                "return"
              } else if (element.isInstanceOf[Call]) {
                "call"
              } else {
                "intermediate"
              }

              val chainNode = ChainNode(
                nodeId = element.id,
                nodeType = astNode.getClass.getSimpleName,
                code = astNode.code,
                methodName = method.name,
                className = className,
                fileName = fileName,
                lineNumber = astNode.lineNumber.getOrElse("?").toString,
                role = role
              )

              // 避免重复添加
              if (!allNodes.exists(_.nodeId == element.id)) {
                allNodes = allNodes :+ chainNode
                if (role != "return") { // 不输出return节点
                  println(s"  ✅ ${role}节点: ${astNode.code} (${method.name})")
                }
              }
            }
          } catch {
            case _: Exception =>
              // 静默忽略错误节点
          }
        }
      }
    } else {
      println("⚠️  数据流分析未找到连接，使用CPG边遍历...")
      // 使用复杂的CPG边遍历逻辑
      allNodes = traceComplexPaths(filteredSourceNodes, filteredSinkNodes, sinkName, sourceName)
    }
  } catch {
    case e: Exception =>
      println(s"⚠️  数据流分析失败: ${e.getMessage}")
      println("使用CPG边遍历...")
      allNodes = traceComplexPaths(filteredSourceNodes, filteredSinkNodes, sinkName, sourceName)
  }

  // 注释：原来的节点收集逻辑已被基于数据流的方法替代

  // 2. 分析节点关系并输出优化的结果
  println(s"\n=== 基于数据流的调用链分析 (共${allNodes.size}个相关节点) ===")

  if (allNodes.isEmpty) {
    println("❌ 未找到有意义的source或sink节点")
    return
  }

  // 按照数据流顺序排序：source -> call -> assignment -> return -> sink
  val sortedNodes = sortDataFlowOrder(allNodes)

  // 输出简化的表格格式，专注于source到sink的数据流顺序
  val headerFormat = "%-8s %-12s %-15s %-40s %-8s %-15s"
  println(headerFormat.format("节点ID", "角色", "方法名", "代码", "行号", "文件"))
  println("=" * 105)

  sortedNodes.foreach { node =>
    // 只显示非return节点
    if (node.role != "return") {
      val roleIcon = node.role match {
        case "source" => "📥"
        case "sink" => "📤"
        case "call" => "📞"
        case "assignment" => "📝"
        case "intermediate" => "🔗"
        case _ => "  "
      }

      println(headerFormat.format(
        node.nodeId.toString,
        s"$roleIcon ${node.role}",
        node.methodName.take(13),
        node.code.take(38),
        node.lineNumber,
        node.fileName.split("/").lastOption.getOrElse(node.fileName).take(13)
      ))
    }
  }

  // 3. 重建调用链 - 显示完整的数据流路径
  println(s"\n=== 完整数据流路径分析 ===")

  val sourceNodesList: List[ChainNode] = sortedNodes.filter(_.role == "source")
  val sinkNodesList: List[ChainNode] = sortedNodes.filter(_.role == "sink")
  val returnNodesList: List[ChainNode] = sortedNodes.filter(_.role == "return")
  val callNodesList: List[ChainNode] = sortedNodes.filter(_.role == "call")
  val assignmentNodesList: List[ChainNode] = sortedNodes.filter(_.role == "assignment")
  val intermediateNodesList: List[ChainNode] = sortedNodes.filter(_.role == "intermediate")

  println(s"✅ 数据流组成: ${sourceNodesList.size} 个source点, ${callNodesList.size} 个调用点, ${assignmentNodesList.size} 个赋值点, ${sinkNodesList.size} 个sink点")

  if (sourceNodesList.nonEmpty && sinkNodesList.nonEmpty) {
    println("\n🔗 完整数据流路径:")

    // 显示完整的数据流序列
    println("  数据流顺序:")
    sortedNodes.filter(_.role != "return").zipWithIndex.foreach { case (node, index) =>
      val arrow = if (index < sortedNodes.filter(_.role != "return").size - 1) " ➡️ " else ""
      val roleIcon = node.role match {
        case "source" => "📥"
        case "sink" => "📤"
        case "call" => "📞"
        case "assignment" => "📝"
        case _ => "🔗"
      }
      println(s"    ${index + 1}. $roleIcon ${node.code} (${node.methodName})$arrow")
    }

    println(s"\n  路径总结:")
    if (sourceNodesList.nonEmpty && sinkNodesList.nonEmpty) {
      val firstSource = sourceNodesList.head
      val lastSink = sinkNodesList.last
      println(s"    📥 起点: ${firstSource.code} (${firstSource.methodName} in ${firstSource.fileName})")
      println(s"    📤 终点: ${lastSink.code} (${lastSink.methodName} in ${lastSink.fileName})")

      if (callNodesList.nonEmpty) {
        println(s"    📞 关键调用: ${callNodesList.map(_.code).mkString(" ➡️ ")}")
      }
    }
  } else {
    println("❌ 无法建立source到sink的连接")
  }

  // 4. 保存详细信息到文件
  saveDetailedChainToFile(sortedNodes, sinkName, sourceName)
}

// 复杂CPG边遍历 - 从sink到source的反向追踪
def traceComplexPaths(sourceNodes: List[flatgraph.GNode], sinkNodes: List[flatgraph.GNode],
                     sinkName: String, sourceName: String): List[ChainNode] = {
  println("开始从sink到source的反向追踪...")

  var pathNodes = List[ChainNode]()

  // 从每个sink节点开始反向追踪到source
  sinkNodes.foreach { sinkNode =>
    val path = traceSinkToSource(sinkNode, sourceNodes, sinkName, sourceName, maxDepth = 10)
    if (path.nonEmpty) {
      println(s"✅ 从sink找到完整路径: ${path.size} 个节点")
      pathNodes = pathNodes ++ path
    }
  }

  // 去重并返回
  pathNodes.groupBy(_.nodeId).map(_._2.head).toList
}

// 从sink节点反向追踪到source节点
def traceSinkToSource(sinkNode: flatgraph.GNode, sourceNodes: List[flatgraph.GNode],
                     sinkName: String, sourceName: String, maxDepth: Int): List[ChainNode] = {
  if (maxDepth <= 0) return List()

  var pathNodes = List[ChainNode]()
  var visitedMethods = Set[Long]()

  try {
    val sinkAst = sinkNode.asInstanceOf[AstNode]
    val sinkMethod = getNodeMethod(sinkAst)

    if (sinkMethod != null) {
      println(s"从sink节点开始: ${sinkAst.code} (方法: ${sinkMethod.name})")

      // 添加sink节点
      pathNodes = pathNodes :+ createChainNode(sinkNode, "sink")

      // 步骤1: 检查同一方法内是否有source
      val sameMethodSources = sourceNodes.filter { sourceNode =>
        try {
          val sourceAst = sourceNode.asInstanceOf[AstNode]
          val sourceMethod = getNodeMethod(sourceAst)
          sourceMethod != null && sourceMethod.id == sinkMethod.id
        } catch {
          case _: Exception => false
        }
      }

      if (sameMethodSources.nonEmpty) {
        println(s"  ✅ 在同一方法中找到source")
        sameMethodSources.foreach { sourceNode =>
          pathNodes = pathNodes :+ createChainNode(sourceNode, "source")
        }
        return pathNodes
      }

      // 步骤2: 查找sink方法中的变量来源（处理case6：返回值传递）
      val sinkVariables = findSinkVariables(sinkNode, sinkMethod)
      sinkVariables.foreach { variable =>
        try {
          val variableAst = variable.asInstanceOf[AstNode]
          println(s"  分析sink变量: ${variableAst.code}")
          val variablePath = traceVariableSource(variable, sourceNodes, sinkMethod, maxDepth - 1)
          pathNodes = pathNodes ++ variablePath
        } catch {
          case _: Exception =>
            println(s"  ❌ 无法分析变量节点")
        }
      }

      // 步骤3: 查找调用这个方法的地方（处理case5：参数传递）
      val methodCalls = cpg.call.name(sinkMethod.name).l
      println(s"  查找调用 ${sinkMethod.name} 方法的地方: 找到 ${methodCalls.size} 个调用")

      methodCalls.foreach { call =>
        try {
          val callerMethod = call.method
          if (!visitedMethods.contains(callerMethod.id)) {
            visitedMethods = visitedMethods + callerMethod.id

            println(s"  找到方法调用: ${callerMethod.name} -> ${sinkMethod.name}")
            println(s"    调用代码: ${call.code}")
            println(s"    调用者文件: ${callerMethod.file.name.headOption.getOrElse("unknown")}")

            // 检查method_full_name属性
            val methodFullName = call.methodFullName
            println(s"    METHOD_FULL_NAME: $methodFullName")

            // 添加调用节点
            pathNodes = pathNodes :+ createChainNode(call, "call")

            // 直接在调用者方法中查找source（关键改进）
            val callerSources = sourceNodes.filter { sourceNode =>
              try {
                val sourceAst = sourceNode.asInstanceOf[AstNode]
                val sourceMethod = getNodeMethod(sourceAst)
                sourceMethod != null && sourceMethod.id == callerMethod.id
              } catch {
                case _: Exception => false
              }
            }

            if (callerSources.nonEmpty) {
              println(s"    ✅ 在调用者方法 ${callerMethod.name} 中找到 ${callerSources.size} 个source")
              callerSources.foreach { sourceNode =>
                val sourceAst = sourceNode.asInstanceOf[AstNode]
                println(s"      Source: ${sourceAst.code} (行${sourceAst.lineNumber.getOrElse("?")})")
                pathNodes = pathNodes :+ createChainNode(sourceNode, "source")
              }
            } else {
              println(s"    ⚠️  在调用者方法 ${callerMethod.name} 中未找到source，继续递归查找...")
              // 递归查找
              val recursivePath = traceSinkToSource(call, sourceNodes, sinkName, sourceName, maxDepth - 1)
              pathNodes = pathNodes ++ recursivePath.filter(_.role != "sink") // 避免重复sink节点
            }
          }
        } catch {
          case _: Exception => // 忽略错误
        }
      }
    }
  } catch {
    case _: Exception => // 忽略错误
  }

  pathNodes
}

// 获取节点所属的方法
def getNodeMethod(astNode: AstNode): io.shiftleft.codepropertygraph.generated.nodes.Method = {
  try {
    astNode match {
      case call: Call => call.method
      case id: Identifier => id.method
      case ret: Return => ret.method
      case _ =>
        cpg.method.filter(_.ast.l.exists(_.id == astNode.id)).headOption.getOrElse(null)
    }
  } catch {
    case _: Exception => null
  }
}

// 查找sink节点中使用的变量（case6关键：找到sink使用的变量）
def findSinkVariables(sinkNode: flatgraph.GNode, sinkMethod: io.shiftleft.codepropertygraph.generated.nodes.Method): List[flatgraph.GNode] = {
  try {
    val sinkAst = sinkNode.asInstanceOf[AstNode]
    println(s"  分析sink节点的参数: ${sinkAst.code}")

    // 查找sink调用的参数
    sinkAst match {
      case call: Call =>
        // 获取调用的参数
        val arguments = call.argument.l
        println(s"    找到 ${arguments.size} 个参数")

        arguments.flatMap { arg =>
          println(s"    参数: ${arg.code}")

          // 如果参数是变量，查找这个变量的定义
          if (arg.code.startsWith("$")) {
            val variableName = arg.code
            // 在同一方法中查找这个变量的赋值 - 改进搜索策略
            val assignments = sinkMethod.ast.isCall.filter { node =>
              try {
                val code = node.code
                code.contains(variableName) && code.contains("=")
              } catch {
                case _: Exception => false
              }
            }.l

            println(s"      查找变量 $variableName 的赋值: ${assignments.size} 个")
            assignments.foreach { assignment =>
              println(s"        赋值: ${assignment.code}")
            }

            // 如果没找到赋值，查找所有包含这个变量的调用
            if (assignments.isEmpty) {
              val variableUsages = sinkMethod.ast.isCall.filter { node =>
                try {
                  node.code.contains(variableName)
                } catch {
                  case _: Exception => false
                }
              }.l
              println(s"      查找变量 $variableName 的使用: ${variableUsages.size} 个")
              variableUsages.foreach { usage =>
                println(s"        使用: ${usage.code}")
              }
              variableUsages
            } else {
              assignments
            }
          } else {
            List()
          }
        }
      case _ => List()
    }
  } catch {
    case _: Exception =>
      println(s"  ❌ 分析sink变量失败")
      List()
  }
}

// 追踪变量的来源（case6关键：追踪变量赋值链）
def traceVariableSource(variable: flatgraph.GNode, sourceNodes: List[flatgraph.GNode],
                       currentMethod: io.shiftleft.codepropertygraph.generated.nodes.Method,
                       maxDepth: Int): List[ChainNode] = {
  if (maxDepth <= 0) return List()

  var pathNodes = List[ChainNode]()

  try {
    val variableAst = variable.asInstanceOf[AstNode]
    println(s"    追踪变量来源: ${variableAst.code}")

    // 添加变量赋值节点
    pathNodes = pathNodes :+ createChainNode(variable, "assignment")

    // 查找这个赋值语句中的方法调用
    val methodCalls = findMethodCallsInAssignment(variableAst)
    methodCalls.foreach { call =>
      println(s"      找到方法调用: ${call.code}")

      // 检查method_full_name
      val methodFullName = call.methodFullName
      println(s"      METHOD_FULL_NAME: $methodFullName")

      // 添加方法调用节点
      pathNodes = pathNodes :+ createChainNode(call, "call")

      // 利用method_full_name查找对应的method节点
      val targetMethod = cpg.method.fullName(methodFullName).headOption
      targetMethod.foreach { tm =>
        println(s"      ✅ 找到目标方法: ${tm.name} (fullName: ${tm.fullName})")

        // 在目标方法中查找source
        val methodSources = sourceNodes.filter { sourceNode =>
          try {
            val sourceAst = sourceNode.asInstanceOf[AstNode]
            val sourceMethod = getNodeMethod(sourceAst)
            sourceMethod != null && sourceMethod.id == tm.id
          } catch {
            case _: Exception => false
          }
        }

        if (methodSources.nonEmpty) {
          println(s"      ✅ 在目标方法 ${tm.name} 中找到 ${methodSources.size} 个source")
          methodSources.foreach { sourceNode =>
            val sourceAst = sourceNode.asInstanceOf[AstNode]
            println(s"        Source: ${sourceAst.code}")
            pathNodes = pathNodes :+ createChainNode(sourceNode, "source")
          }

          // 查找返回语句
          val returnStmts = tm.ast.isReturn.l
          returnStmts.foreach { ret =>
            println(s"        返回语句: ${ret.code}")
            pathNodes = pathNodes :+ createChainNode(ret, "return")
          }
        } else {
          println(s"      ⚠️  在目标方法 ${tm.name} 中未找到source，继续递归查找...")

          // 如果在目标方法中没找到source，继续在目标方法中查找变量来源
          val targetMethodVariables = findSinkVariables(call, tm)
          targetMethodVariables.foreach { targetVar =>
            val recursivePath = traceVariableSource(targetVar, sourceNodes, tm, maxDepth - 1)
            pathNodes = pathNodes ++ recursivePath
          }
        }
      }
    }

    // 如果没有找到方法调用，直接在当前方法中查找所有方法调用
    if (methodCalls.isEmpty) {
      println(s"    未在赋值中找到方法调用，查找当前方法中的所有方法调用...")
      val allMethodCalls = currentMethod.ast.isCall.nameNot("<operator>.*").nameNot("echo").nameNot("isset").l
      allMethodCalls.foreach { call =>
        val methodFullName = call.methodFullName
        if (methodFullName.nonEmpty && !methodFullName.contains("<operator>")) {
          println(s"      检查方法调用: ${call.code} (METHOD_FULL_NAME: $methodFullName)")

          val targetMethod = cpg.method.fullName(methodFullName).headOption
          targetMethod.foreach { tm =>
            val methodSources = sourceNodes.filter { sourceNode =>
              try {
                val sourceAst = sourceNode.asInstanceOf[AstNode]
                val sourceMethod = getNodeMethod(sourceAst)
                sourceMethod != null && sourceMethod.id == tm.id
              } catch {
                case _: Exception => false
              }
            }

            if (methodSources.nonEmpty) {
              println(s"      ✅ 在方法 ${tm.name} 中找到source")
              pathNodes = pathNodes :+ createChainNode(call, "call")
              methodSources.foreach { sourceNode =>
                pathNodes = pathNodes :+ createChainNode(sourceNode, "source")
              }

              val returnStmts = tm.ast.isReturn.l
              returnStmts.foreach { ret =>
                pathNodes = pathNodes :+ createChainNode(ret, "return")
              }
            }
          }
        }
      }
    }
  } catch {
    case _: Exception =>
      println(s"    ❌ 追踪变量来源失败")
  }

  pathNodes
}

// 删除：findMethodCallsInAssignment() - 已被简化的逻辑替代

// 按照数据流顺序排序：source -> call -> assignment -> return -> sink
def sortDataFlowOrder(nodes: List[ChainNode]): List[ChainNode] = {
  // 定义角色的优先级顺序
  def getRolePriority(role: String): Int = role match {
    case "source" => 1      // source最先
    case "call" => 2        // 然后是方法调用
    case "assignment" => 3  // 然后是变量赋值
    case "return" => 4      // 然后是返回语句
    case "sink" => 5        // sink最后
    case _ => 6
  }

  // 按照角色优先级和行号排序
  nodes.sortBy { node =>
    val priority = getRolePriority(node.role)
    val lineNum = node.lineNumber.toIntOption.getOrElse(999)
    val isGlobal = if (node.methodName == "<global>") 0 else 1  // global方法优先

    (priority, isGlobal, node.fileName, lineNum)
  }
}

// 创建ChainNode的辅助函数
def createChainNode(node: flatgraph.GNode, role: String): ChainNode = {
  try {
    val astNode = node.asInstanceOf[AstNode]
    val method = getNodeMethod(astNode)

    val className = if (method != null) {
      method.typeDecl.name.headOption.getOrElse("unknown")
    } else {
      "unknown"
    }

    val methodName = if (method != null) {
      method.name
    } else {
      "unknown"
    }

    val fileName = astNode.file.name.headOption.getOrElse("unknown")

    ChainNode(
      nodeId = node.id,
      nodeType = astNode.getClass.getSimpleName,
      code = astNode.code,
      methodName = methodName,
      className = className,
      fileName = fileName,
      lineNumber = astNode.lineNumber.getOrElse("?").toString,
      role = role
    )
  } catch {
    case _: Exception =>
      ChainNode(
        nodeId = node.id,
        nodeType = "unknown",
        code = "unknown",
        methodName = "unknown",
        className = "unknown",
        fileName = "unknown",
        lineNumber = "?",
        role = role
      )
  }
}

// 删除：createDirectPath() - 已被新的反向追踪替代
// 在赋值语句中查找方法调用
def findMethodCallsInAssignment(assignment: AstNode): List[Call] = {
  try {
    // 查找赋值语句右侧的方法调用
    assignment match {
      case call: Call =>
        // 如果赋值语句本身包含方法调用
        val rightSide = call.argument.l.filter(_.order > 1) // 获取赋值右侧
        rightSide.flatMap {
          case callArg: Call => List(callArg)
          case _ => List()
        }
      case _ =>
        // 在AST中查找子节点中的方法调用
        assignment.ast.isCall.nameNot("<operator>.*").l
    }
  } catch {
    case _: Exception => List()
  }
}

// 删除：findMethodCallPath() 和 findReturnValuePath() - 已被新的反向追踪替代

// 传统收集方法（作为回退方案）
def collectNodesTraditionalWay(sourceNodes: List[flatgraph.GNode], sinkNodes: List[flatgraph.GNode],
                              allNodes: scala.collection.mutable.ListBuffer[ChainNode],
                              sinkName: String, sourceName: String): Unit = {
  println("使用传统方法收集节点...")

  // 收集source节点
  sourceNodes.foreach { node =>
    try {
      val astNode = node.asInstanceOf[AstNode]
      val method = astNode match {
        case call: Call => call.method
        case id: Identifier => id.method
        case _ =>
          cpg.method.filter(_.ast.l.exists(_.id == astNode.id)).headOption.getOrElse(null)
      }

      if (method != null) {
        val className = method.typeDecl.name.headOption.getOrElse("unknown")
        val fileName = astNode.file.name.headOption.getOrElse("unknown")

        val chainNode = ChainNode(
          nodeId = node.id,
          nodeType = astNode.getClass.getSimpleName,
          code = astNode.code,
          methodName = method.name,
          className = className,
          fileName = fileName,
          lineNumber = astNode.lineNumber.getOrElse("?").toString,
          role = "source"
        )
        allNodes += chainNode
        println(s"  ✅ Source节点: ${astNode.code} (${method.name})")
      }
    } catch {
      case _: Exception =>
        // 静默忽略错误节点
    }
  }

  // 收集sink节点
  sinkNodes.foreach { node =>
    try {
      val astNode = node.asInstanceOf[AstNode]
      val method = astNode match {
        case call: Call => call.method
        case _ =>
          cpg.method.filter(_.ast.l.exists(_.id == astNode.id)).headOption.getOrElse(null)
      }

      if (method != null) {
        val className = method.typeDecl.name.headOption.getOrElse("unknown")
        val fileName = astNode.file.name.headOption.getOrElse("unknown")

        val chainNode = ChainNode(
          nodeId = node.id,
          nodeType = astNode.getClass.getSimpleName,
          code = astNode.code,
          methodName = method.name,
          className = className,
          fileName = fileName,
          lineNumber = astNode.lineNumber.getOrElse("?").toString,
          role = "sink"
        )
        allNodes += chainNode
        println(s"  ✅ Sink节点: ${astNode.code} (${method.name})")
      }
    } catch {
      case _: Exception =>
        // 静默忽略错误节点
    }
  }
}

  // 注释：重复的代码已被移除


// 保存详细调用链到文件
def saveDetailedChainToFile(nodes: List[ChainNode], sinkName: String, sourceName: String): Unit = {
  try {
    import java.io.{File, PrintWriter}
    val filename = s"detailed_chain_${sinkName}_to_${sourceName}.txt"
    val writer = new PrintWriter(new File(filename))

    writer.println(s"=== 详细调用链分析结果 ===")
    writer.println(s"生成时间: ${java.time.LocalDateTime.now()}")
    writer.println(s"Sink: $sinkName")
    writer.println(s"Source: $sourceName")
    writer.println(s"总节点数: ${nodes.length}")
    writer.println()

    // 输出详细的节点信息
    val headerFormat = "%-8s %-10s %-15s %-20s %-15s %-50s %-8s %-20s"
    writer.println(headerFormat.format("节点ID", "角色", "类型", "类名", "方法名", "代码", "行号", "文件"))
    writer.println("=" * 150)

    // 处理ChainNode类型的节点
    nodes.foreach { node =>
      try {
        writer.println(headerFormat.format(
          node.nodeId.toString,
          node.role,
          node.nodeType,
          node.className.take(18),
          node.methodName.take(13),
          node.code.take(48),
          node.lineNumber,
          node.fileName.split("/").lastOption.getOrElse(node.fileName).take(18)
        ))
      } catch {
        case _: Exception =>
          writer.println(s"节点信息获取失败: ${node.nodeId}")
      }
    }

    writer.close()
    println(s"\n✅ 详细调用链已保存到文件: $filename")
  } catch {
    case e: Exception =>
      println(s"❌ 保存文件失败: ${e.getMessage}")
  }
}

// 方法级别分析（借鉴kualei.sc的思路）
def analyzeMethodLevel(cpg: Cpg, sinkName: String, sourceName: String): Unit = {
  // 找到包含source的方法
  val sourceMethods = cpg.call.code(s".*$sourceName.*").method.l ++
                     cpg.identifier.name(sourceName).method.l

  // 找到包含sink的方法
  val sinkMethods = cpg.call.name(sinkName).method.l

  println(s"包含source的方法: ${sourceMethods.size} 个")
  sourceMethods.take(3).foreach { method =>
    val file = method.file.name.headOption.getOrElse("unknown")
    println(s"  ${method.name} in $file")
  }
  if (sourceMethods.length > 3) {
    println(s"  ... 还有 ${sourceMethods.length - 3} 个方法")
  }

  println(s"包含sink的方法: ${sinkMethods.size} 个")
  sinkMethods.take(3).foreach { method =>
    val file = method.file.name.headOption.getOrElse("unknown")
    println(s"  ${method.name} in $file")
  }
  if (sinkMethods.length > 3) {
    println(s"  ... 还有 ${sinkMethods.length - 3} 个方法")
  }

  // 查找方法调用关系
  println("\n方法调用关系分析:")
  sinkMethods.foreach { sinkMethod =>
    println(s"\n从sink方法 ${sinkMethod.name} 开始追踪:")

    // 查找调用这个sink方法的其他方法
    val callers = cpg.call.name(sinkMethod.name).method.l
    callers.foreach { caller =>
      val file = caller.file.name.headOption.getOrElse("unknown")
      println(s"  被 ${caller.name} 调用 (in $file)")

      // 检查调用者是否包含source
      val hasSource = caller.call.code(s".*$sourceName.*").nonEmpty ||
                     caller.ast.isIdentifier.name(sourceName).nonEmpty

      if (hasSource) {
        println(s"    ✅ ${caller.name} 包含source!")

        // 显示详细的调用链
        println(s"    调用链: ${caller.name} -> ${sinkMethod.name}")

        // 显示source和sink的具体位置
        caller.call.code(s".*$sourceName.*").foreach { sourceCall =>
          println(s"      Source: ${sourceCall.code} (行${sourceCall.lineNumber.getOrElse("?")})")
        }

        sinkMethod.call.name(sinkName).foreach { sinkCall =>
          println(s"      Sink: ${sinkCall.code} (行${sinkCall.lineNumber.getOrElse("?")})")
        }
      }
    }
  }

  // 简化的跨类调用分析
  println("\n跨类调用分析:")
  val classes = cpg.typeDecl.nameNot("<global>").l
  val relevantClasses = classes.filter { cls =>
    val classMethods = cls.method.l
    val hasSource = classMethods.exists { method =>
      method.call.code(s".*$sourceName.*").nonEmpty ||
      method.ast.isIdentifier.name(sourceName).nonEmpty
    }
    val hasSink = classMethods.exists { method =>
      method.call.name(sinkName).nonEmpty
    }
    hasSource || hasSink
  }

  if (relevantClasses.nonEmpty) {
    relevantClasses.foreach { cls =>
      val classMethods = cls.method.l
      val hasSource = classMethods.exists { method =>
        method.call.code(s".*$sourceName.*").nonEmpty ||
        method.ast.isIdentifier.name(sourceName).nonEmpty
      }
      val hasSink = classMethods.exists { method =>
        method.call.name(sinkName).nonEmpty
      }

      println(s"类 ${cls.name}:")
      if (hasSource) println(s"  ✅ 包含source")
      if (hasSink) println(s"  ✅ 包含sink")
      if (hasSource && hasSink) println(s"  ⚠️  同时包含source和sink，存在潜在风险")
    }
  } else {
    println("未找到包含source或sink的类")
  }
}

// 删除：quickAnalysis() - 功能与主要分析重复

// 新增：专门的sink-source配对分析函数
def analyzeSinkSourcePairs(cpg: Cpg): Unit = {
  println("\n=== Sink-Source配对分析 ===")

  // 定义常见的sink和source
  val commonSinks = List("system", "exec", "shell_exec", "eval", "file_put_contents", "move_uploaded_file", "fopen", "file_get_contents")
  val commonSources = List("_GET", "_POST", "_REQUEST", "_COOKIE", "_FILES")

  var foundPairs = 0

  commonSinks.foreach { sinkName =>
    commonSources.foreach { sourceName =>
      val sinks = filterUsefulNodes(cpg.call.name(sinkName).l)
      val sources = if (sourceName.startsWith("_")) {
        filterUsefulNodes(cpg.call.code(s".*\\$$sourceName.*").l ++ cpg.identifier.name(sourceName).l)
      } else {
        filterUsefulNodes(cpg.call.code(s".*$sourceName.*").l)
      }

      if (sinks.nonEmpty && sources.nonEmpty) {
        foundPairs += 1
        println(s"\n🎯 发现配对: $sourceName ➡️ $sinkName")
        println(s"   Source节点: ${sources.size} 个, Sink节点: ${sinks.size} 个")

        // 显示第一个source和sink的详细信息
        try {
          val firstSource = sources.head.asInstanceOf[AstNode]
          val firstSink = sinks.head.asInstanceOf[AstNode]

          val sourceFile = firstSource.file.name.headOption.getOrElse("unknown").split("/").last
          val sinkFile = firstSink.file.name.headOption.getOrElse("unknown").split("/").last

          println(s"   📥 Source: ${firstSource.code} (${sourceFile}:${firstSource.lineNumber.getOrElse("?")})")
          println(s"   📤 Sink: ${firstSink.code} (${sinkFile}:${firstSink.lineNumber.getOrElse("?")})")

          if (sourceFile == sinkFile) {
            println(s"   ✅ 在同一文件中: $sourceFile")
          } else {
            println(s"   ⚠️  跨文件调用: $sourceFile ➡️ $sinkFile")
          }
        } catch {
          case _: Exception => // 忽略错误
        }
      }
    }
  }

  if (foundPairs == 0) {
    println("❌ 未找到任何sink-source配对")
  } else {
    println(s"\n✅ 总共找到 $foundPairs 个sink-source配对")
  }
}

println("\n=== 简化后的使用示例 ===")
println("🚀 主要方法 (推荐):")
println("  reconstructCallChain(cpg, \"system\", \"_GET\")")
println("  reconstructCallChain(cpg, \"move_uploaded_file\", \"_POST\")")
println("\n🔍 混合分析:")
println("  hybridTrace(cpg, \"system\", \"_GET\", 10)")
println("\n📊 配对分析:")
println("  analyzeSinkSourcePairs(cpg)")

println("\n=== 简化特点 ===")
println("✅ 基于数据流分析 + CPG反向追踪")
println("✅ 智能处理跨类调用 (case5, case6)")
println("✅ 精确的source到sink路径追踪")
println("✅ 保留传统方法作为备用")
println("✅ 优化的输出顺序和格式")

println("\n=== 推荐测试命令 ===")
println("🎯 主要分析: reconstructCallChain(cpg, \"system\", \"_GET\")")
println("🔍 配对分析: analyzeSinkSourcePairs(cpg)")
println("📋 混合分析: hybridTrace(cpg, \"move_uploaded_file\", \"_POST\")")

// 执行示例
println("\n🚀 脚本已简化完成，删除了冗余功能，保留核心算法和传统备用方法")
