joern> hybridTrace(cpg, "system", "_GET", 5)

=== 混合追踪方法: system -> _GET ===

--- 主要方法: 完整调用链重建 ---
重建完整调用链（基于数据流分析）...

收集source和sink节点:
找到 7 个source节点, 1 个sink节点

进行数据流分析...
✅ 数据流分析找到 4 条有效路径
数据流路径包含 7 个相关节点

=== 基于数据流的调用链分析 (共0个相关节点) ===
❌ 未找到有意义的source或sink节点
                                                                                                                                                                                                                                           

joern> val sink = cpg.call.name("system").l
val sink: List[io.shiftleft.codepropertygraph.generated.nodes.Call] = List(
  Call(
    argumentIndex = -1,
    argumentName = None,
    code = "system($cmd)",
    columnNumber = None,
    dispatchType = "STATIC_DISPATCH",
    dynamicTypeHintFullName = IndexedSeq("system"),
    lineNumber = Some(value = 5),
    methodFullName = "system",
    name = "system",
    offset = None,
    offsetEnd = None,
    order = 4,
    possibleTypes = IndexedSeq(),
    signature = "<unresolvedSignature>(1)",
    typeFullName = "ANY"
  )
)
                                                                                                                                                                                                                                           
joern> val source = cpg.identifier.name("_GET").l
val source: List[io.shiftleft.codepropertygraph.generated.nodes.Identifier] = List(
  Identifier(
    argumentIndex = 1,
    argumentName = None,
    code = "$_GET",
    columnNumber = None,
    dynamicTypeHintFullName = IndexedSeq(),
    lineNumber = Some(value = 4),
    name = "_GET",
    offset = None,
    offsetEnd = None,
    order = 1,
    possibleTypes = IndexedSeq(),
    typeFullName = "ANY"
  ),
  Identifier(
    argumentIndex = 1,
    argumentName = None,
    code = "$_GET",
    columnNumber = None,
    dynamicTypeHintFullName = IndexedSeq(),
    lineNumber = Some(value = 4),
    name = "_GET",
    offset = None,
    offsetEnd = None,
    order = 1,
    possibleTypes = IndexedSeq(),
    typeFullName = "ANY"
  )
)
                                                                                                                                                                                                                                           
val sink: List[io.shiftleft.codepropertygraph.generated.nodes.Call]

joern> sink.reachableByFlows(source).p
val res2: List[String] = List(
  """
┌──────────┬──────────────────────────────────────────────┬────┬──────┬─────────┐                                                                                                                                                          
│nodeType  │tracked                                       │line│method│file     │                                                                                                                                                          
├──────────┼──────────────────────────────────────────────┼────┼──────┼─────────┤                                                                                                                                                          
│Identifier│isset($_GET["cmd"]) ? $_GET["cmd"] : ""       │4   │greet │case2.php│                                                                                                                                                          
│Call      │isset($_GET["cmd"])                           │4   │greet │case2.php│                                                                                                                                                          
│Call      │isset($_GET["cmd"]) ? $_GET["cmd"] : ""       │4   │greet │case2.php│                                                                                                                                                          
│Call      │isset($_GET["cmd"]) ? $_GET["cmd"] : ""       │4   │greet │case2.php│                                                                                                                                                          
│Identifier│$cmd = isset($_GET["cmd"]) ? $_GET["cmd"] : ""│4   │greet │case2.php│                                                                                                                                                          
│Identifier│system($cmd)                                  │5   │greet │case2.php│                                                                                                                                                          
│Call      │system($cmd)                                  │5   │greet │case2.php│                                                                                                                                                          
└──────────┴──────────────────────────────────────────────┴────┴──────┴─────────┘""",                                                                                                                                                      
  """
┌──────────┬──────────────────────────────────────────────┬────┬──────┬─────────┐                                                                                                                                                          
│nodeType  │tracked                                       │line│method│file     │                                                                                                                                                          
├──────────┼──────────────────────────────────────────────┼────┼──────┼─────────┤                                                                                                                                                          
│Identifier│isset($_GET["cmd"]) ? $_GET["cmd"] : ""       │4   │greet │case2.php│                                                                                                                                                          
│Call      │isset($_GET["cmd"]) ? $_GET["cmd"] : ""       │4   │greet │case2.php│                                                                                                                                                          
│Call      │isset($_GET["cmd"]) ? $_GET["cmd"] : ""       │4   │greet │case2.php│                                                                                                                                                          
│Identifier│$cmd = isset($_GET["cmd"]) ? $_GET["cmd"] : ""│4   │greet │case2.php│                                                                                                                                                          
│Identifier│system($cmd)                                  │5   │greet │case2.php│                                                                                                                                                          
│Call      │system($cmd)                                  │5   │greet │case2.php│                                                                                                                                                          
└──────────┴──────────────────────────────────────────────┴────┴──────┴─────────┘"""                                                                                                                                                       
)
                                                                                                                                                                                                                                           
joern> 
